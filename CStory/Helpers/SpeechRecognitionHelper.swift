//
//  SpeechRecognitionHelper.swift
//  CStory
//
//  Created by NZUE on 2025/7/12.
//

import AVFoundation
import Combine
import Speech

/// 语音识别管理器
///
/// 重构后的语音识别管理器，解决了AVAudioEngine的tap管理问题
/// 主要改进：
/// - 更安全的tap安装和移除机制
/// - 更好的错误处理和状态管理
/// - 避免重复tap安装导致的崩溃
class SpeechRecognitionHelper: ObservableObject {
  /// 实时转录的文本
  @Published var transcribedText: String = ""
  /// 音频输入级别（0.0-1.0）
  @Published var audioLevel: CGFloat = 0.0
  /// 录音状态
  @Published var isRecording: Bool = false

  private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
  private var recognitionTask: SFSpeechRecognitionTask?
  private let audioEngine = AVAudioEngine()
  let speechRecognizer = SFSpeechRecognizer(locale: Locale(identifier: "zh-CN"))

  /// 标记是否已安装Tap
  private var isTapInstalled = false
  /// 音频会话是否已配置
  private var audioSessionConfigured = false
  /// 串行队列确保线程安全
  private let audioQueue = DispatchQueue(label: "com.cstory.speech.audio", qos: .userInitiated)

  /// 开始录音和识别
  func startRecording() {
    // 检查权限
    guard checkPermissions() else {
      print("❌ 语音识别权限不足")
      return
    }

    // 防止重复启动
    guard !isRecording else {
      print("⚠️ 录音已在进行中")
      return
    }

    audioQueue.async { [weak self] in
      self?.performStartRecording()
    }
  }

  /// 停止录音并返回识别结果
  func stopRecording() -> String {
    let finalText = transcribedText

    audioQueue.sync { [weak self] in
      self?.performStopRecording(isCancelled: false)
    }

    DispatchQueue.main.async { [weak self] in
      self?.transcribedText = ""
      self?.isRecording = false
    }
    return finalText
  }

  /// 取消录音（不保存结果）
  func cancelRecording() {
    audioQueue.sync { [weak self] in
      self?.performStopRecording(isCancelled: true)
    }

    DispatchQueue.main.async { [weak self] in
      self?.transcribedText = ""
      self?.isRecording = false
    }
  }

  // MARK: - Private Methods

  /// 检查权限
  private func checkPermissions() -> Bool {
    let speechAuthStatus = SFSpeechRecognizer.authorizationStatus()
    let microphoneAuthStatus = AVCaptureDevice.authorizationStatus(for: .audio)

    return speechAuthStatus == .authorized && microphoneAuthStatus == .authorized
  }

  /// 执行开始录音（在音频队列中）
  private func performStartRecording() {
    do {
      // 先清理之前的状态
      cleanupAudioResources()

      // 配置音频会话
      try configureAudioSession()

      // 设置语音识别
      try setupSpeechRecognition()

      // 启动音频引擎
      try startAudioEngine()

      DispatchQueue.main.async { [weak self] in
        self?.isRecording = true
      }

      print("✅ 语音识别启动成功")

    } catch {
      print("❌ 语音识别启动失败: \(error)")
      cleanupAudioResources()
      DispatchQueue.main.async { [weak self] in
        self?.isRecording = false
      }
    }
  }

  /// 执行停止录音（在音频队列中）
  private func performStopRecording(isCancelled: Bool = false) {
    cleanupAudioResources(isCancelled: isCancelled)
    if isCancelled {
      print("✅ 语音识别已取消")
    } else {
      print("✅ 语音识别已停止")
    }
  }

  /// 配置音频会话
  private func configureAudioSession() throws {
    let audioSession = AVAudioSession.sharedInstance()

    // 检查当前音频会话状态
    if audioSession.category != .record {
      // 处理蓝牙音频选项的兼容性问题
      var categoryOptions: AVAudioSession.CategoryOptions = [.duckOthers]

      // 根据iOS版本选择合适的蓝牙选项
      if #available(iOS 10.0, *) {
        // iOS 10.0+ 使用 allowBluetooth，但在某些新版本中可能被弃用
        categoryOptions.insert(.allowBluetooth)
      }

      try audioSession.setCategory(.record, mode: .measurement, options: categoryOptions)
    }

    // 只有在未激活时才激活
    if !audioSession.isOtherAudioPlaying {
      try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
    }

    audioSessionConfigured = true
    print("✅ 音频会话配置成功")
  }

  /// 设置语音识别
  private func setupSpeechRecognition() throws {
    guard let speechRecognizer = speechRecognizer, speechRecognizer.isAvailable else {
      throw SpeechRecognitionError.recognizerUnavailable
    }

    // 创建识别请求
    recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
    guard let recognitionRequest = recognitionRequest else {
      throw SpeechRecognitionError.requestCreationFailed
    }

    recognitionRequest.shouldReportPartialResults = true

    // 设置本地识别（如果可用）
    if #available(iOS 13.0, *) {
      recognitionRequest.requiresOnDeviceRecognition = false
    }

    // 开始识别任务
    recognitionTask = speechRecognizer.recognitionTask(with: recognitionRequest) {
      [weak self] result, error in
      DispatchQueue.main.async {
        if let result = result {
          self?.transcribedText = result.bestTranscription.formattedString
        }

        if let error = error {
          // 检查是否是正常的取消/结束错误
          if self?.isNormalSpeechRecognitionEnd(error) == true {
            print("ℹ️ 语音识别结束: \(error.localizedDescription)")
          } else {
            print("❌ 语音识别错误: \(error)")
          }
          // 不要在这里调用performStopRecording，因为可能是正常的取消操作
        }
      }
    }

    print("✅ 语音识别设置成功")
  }

  /// 启动音频引擎
  private func startAudioEngine() throws {
    let inputNode = audioEngine.inputNode

    // 确保没有已安装的tap
    if isTapInstalled {
      inputNode.removeTap(onBus: 0)
      isTapInstalled = false
    }

    // 重置输入节点（重要：这有助于清理之前的状态）
    inputNode.reset()

    let recordingFormat = inputNode.outputFormat(forBus: 0)

    // 安装新的tap
    inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) {
      [weak self] buffer, _ in
      // 处理语音识别
      self?.recognitionRequest?.append(buffer)

      // 计算音量级别
      let level = self?.calculateLevel(buffer) ?? 0
      DispatchQueue.main.async {
        self?.audioLevel = level
      }
    }

    isTapInstalled = true

    // 准备并启动音频引擎
    audioEngine.prepare()
    try audioEngine.start()

    print("✅ 音频引擎启动成功")
  }

  /// 计算音频缓冲区的音量级别
  private func calculateLevel(_ buffer: AVAudioPCMBuffer) -> CGFloat {
    guard let channelData = buffer.floatChannelData?[0] else { return 0 }
    let frameLength = UInt32(buffer.frameLength)

    var sum: Float = 0
    for i in 0..<Int(frameLength) {
      let sample = channelData[i]
      sum += sample * sample
    }

    let rms = sqrt(sum / Float(frameLength))
    return CGFloat(min(rms * 10, 1.0))  // 限制在0-1范围内
  }

  /// 判断是否是正常的语音识别结束错误
  private func isNormalSpeechRecognitionEnd(_ error: Error) -> Bool {
    let nsError = error as NSError

    // kAFAssistantErrorDomain 错误
    if nsError.domain == "kAFAssistantErrorDomain" {
      switch nsError.code {
      case 1110:  // No speech detected
        return true
      case 216:  // 用户取消
        return true
      default:
        return false
      }
    }

    // kLSRErrorDomain 错误
    if nsError.domain == "kLSRErrorDomain" {
      switch nsError.code {
      case 301:  // Recognition request was canceled
        return true
      default:
        return false
      }
    }

    return false
  }

  /// 清理音频资源
  private func cleanupAudioResources(isCancelled: Bool = false) {
    // 停止音频引擎
    if audioEngine.isRunning {
      audioEngine.stop()
    }

    // 移除tap（如果已安装）
    if isTapInstalled {
      audioEngine.inputNode.removeTap(onBus: 0)
      isTapInstalled = false
    }

    // 清理识别相关资源
    if isCancelled {
      // 取消时直接取消任务，不发送endAudio
      recognitionTask?.cancel()
    } else {
      // 正常停止时发送endAudio
      recognitionRequest?.endAudio()
    }
    recognitionRequest = nil
    recognitionTask = nil

    // 重置音频级别
    DispatchQueue.main.async { [weak self] in
      self?.audioLevel = 0
    }

    // 重置音频会话（如果需要）
    if audioSessionConfigured {
      do {
        let audioSession = AVAudioSession.sharedInstance()
        // 只有在没有其他音频播放时才停用会话
        if !audioSession.isOtherAudioPlaying {
          try audioSession.setActive(false, options: .notifyOthersOnDeactivation)
        }
        audioSessionConfigured = false
        print("✅ 音频会话已重置")
      } catch {
        print("⚠️ 音频会话重置失败: \(error)")
        // 即使失败也标记为未配置，避免状态不一致
        audioSessionConfigured = false
      }
    }
  }
}

// MARK: - Error Types

enum SpeechRecognitionError: Error, LocalizedError {
  case recognizerUnavailable
  case requestCreationFailed
  case audioEngineStartFailed
  case permissionDenied

  var errorDescription: String? {
    switch self {
    case .recognizerUnavailable:
      return "语音识别器不可用"
    case .requestCreationFailed:
      return "无法创建语音识别请求"
    case .audioEngineStartFailed:
      return "音频引擎启动失败"
    case .permissionDenied:
      return "语音识别权限被拒绝"
    }
  }
}
